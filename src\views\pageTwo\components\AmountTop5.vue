<!--
  机构放款金额Top5组件
  功能：
  1. 展示机构放款金额的前5名数据
  2. 提供3D柱状图可视化展示
  3. 显示累计放款金额和笔数
  4. 支持图表交互和提示
-->
<template>
  <div class="AmountTop5-dataAcquisition">
    <!-- 头部标题区域 -->
    <div class="AmountTop5-headerImg">
      <div>机构放款动态</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <div class="AmountTop5-bottomClass">
      <!-- 顶部统计信息 -->
      <div class="AmountTop5-top-summary">
        <span>累计放款金额：</span>
        <span class="AmountTop5-highlight">{{ amount }}</span
        >亿元 <span class="AmountTop5-highlight2">{{ parseInt(count) }}</span
        >笔
      </div>
      <!-- 图表容器 -->
      <div ref="barChart" class="AmountTop5-echart-bar"></div>
    </div>
    <dialog-bar-park2
      :visible="dialogVisible"
      :data="parkList"
      title="机构放款动态"
      :display-count="12"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBarPark2 from "@/views/components/dialogBar-park2.vue";

export default {
  name: "AmountTop5",
  components: { DialogBarPark2 },
  data() {
    return {
      dialogVisible: false,
      // 银行名称列表
      banks: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
      ],
      values: [], // 放款金额数据（单位：亿）
      amount: 0, // 累计放款金额
      count: 0, // 累计放款笔数
      counts: [3222, 2888, 2540, 2100, 1800], // 放款笔数数据
      dialogAutoPlayTimer: null, // 自动播放定时器
      timerIndex: 0, // 当前播放的索引
      addResizeListener: false, // 是否添加了resize监听器
      parkList: [],
      top5List: [], // Top5数据列表
      chart: null, // 图表实例
      scrollTimer: null, // 自动滚动定时器
      currentIndex: 0, // 当前滚动索引
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
    };
  },
  mounted() {
    // 获取数据并初始化图表
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "累计放款笔数",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构放款金额",
      },
    ]).then((res) => {
      this.amount = res.data.data[0].indexValue;
      this.count = res.data.data[1].indexValue;
      this.top5List = JSON.parse(res.data.data[2].bizContent);
      this.parkList = this.top5List
        .map((item) => {
          return { ...item, value: item.loanAmt.replace("亿元", "") };
        })
        .sort((a, b) => b.value - a.value);
      // 处理数据：去除单位并转换为数字
      this.values = this.top5List.map((item) =>
        item.loanAmt.replace("亿元", "")
      );
      this.values = this.values.map((item) => Number(item));

      this.counts = this.top5List.map((item) => item.loanSum);
      this.banks = this.top5List.map((item) => item.shortName);
      this.initBarChart();
      // 启动自动滚动
      this.startAutoScroll(this.banks.length);
    });
  },
  methods: {
    // 启动自动滚动
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      this.scrollTimer = setInterval(() => {
        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 获取当前要显示的5组数据
        const startIndex = this.currentIndex;
        const endIndex = startIndex + 5;

        // 处理索引范围，确保不超出数组边界
        let displayData = [];
        let displayValues = [];
        let displayCounts = [];

        for (let i = 0; i < 5; i++) {
          const actualIndex = (startIndex + i) % totalItems;
          displayData.push(this.banks[actualIndex]);
          displayValues.push(this.values[actualIndex]);
          displayCounts.push(this.counts[actualIndex]);
        }
        console.log(displayValues, displayValues, "copydisplayValues");
        let copydisplayValues = displayValues.map((item) => {
          return item * 1.06;
        });
        // 更新图表
        this.chart.setOption({
          xAxis: {
            data: displayData,
          },
          series: [
            // 主柱状图 - 正面
            {
              name: "main",
              tooltip: {
                show: false,
              },
              type: "bar",
              barWidth: this.$autoFontSize(20),
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    1,
                    0,
                    0,
                    [
                      {
                        offset: 0,
                        color: "rgba(42,205,253, 0.6)", // 底部颜色
                      },
                      {
                        offset: 0.6,
                        color: "rgba(42,205,253, 0.8)", // 中间颜色
                      },
                      {
                        offset: 1,
                        color: "rgba(42,205,253, 1)", // 顶部颜色
                      },
                    ],
                    false
                  ),
                },
              },
              barGap: 0,
              data: displayValues,
            },
            // 右侧面 - 深度效果
            {
              name: "right",
              tooltip: {
                show: false,
              },
              type: "bar",
              barWidth: this.$autoFontSize(10),
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    1,
                    0,
                    0,
                    [
                      {
                        offset: 0,
                        color: "rgba(42,205,253, 0.4)", // 底部颜色
                      },
                      {
                        offset: 0.6,
                        color: "rgba(42,205,253, 0.6)", // 中间颜色
                      },
                      {
                        offset: 1,
                        color: "rgba(42,205,253, 0.8)", // 顶部颜色
                      },
                    ],
                    false
                  ),
                },
              },
              barGap: 0,
              data: copydisplayValues,
            },
            // 顶面 - 3D顶部效果
            {
              name: "top3d",
              tooltip: {
                show: false,
              },
              type: "pictorialBar",
              itemStyle: {
                borderWidth: 1,
                borderColor: "#000",
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: "rgba(42,205,253, 1)",
                  },
                  {
                    offset: 1,
                    color: "#419EFF",
                  },
                ]),
              },
              symbol: "path://M 0,0 l 80,0 l -15,10 l -80,0 z",
              symbolSize: ["31", "7"],
              symbolOffset: ["-4", "-7"],
              symbolPosition: "end",
              data: displayValues,
              z: 3,
            },
            // 标签系列
            {
              type: "bar",
              label: {
                show: true,
                position: "top",
                padding: [0, 0, this.$autoFontSize(5), 0],
                formatter: (params) => {
                  const value = displayValues[params.dataIndex];
                  const count = displayCounts[params.dataIndex];
                  return `{gold|${value}亿元}\n{blue|${count}}`;
                },
                rich: {
                  gold: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(14),
                    padding: [
                      0,
                      this.$autoFontSize(28),
                      this.$autoFontSize(5),
                      0,
                    ],
                    align: "center",
                  },
                  blue: {
                    color: "#7cebff",
                    padding: [0, this.$autoFontSize(28), 0, 0],
                    fontSize: this.$autoFontSize(14),
                    align: "center",
                  },
                },
              },
              itemStyle: {
                color: "transparent",
              },
              tooltip: {},
              data: copydisplayValues,
            },
          ],
        });
      }, this.scrollSpeed);
    },

    // 初始化3D柱状图
    initBarChart() {
      this.chart = echarts.init(this.$refs.barChart);
      const primaryColor = "42,205,253"; // 主色调
      const isMaxShow = true; // 是否显示最大值

      // 只取前5条数据进行初始显示
      const displayBanks = this.banks.slice(0, 5);
      const displayValues = this.values.slice(0, 5);
      const displayCounts = this.counts.slice(0, 5);
      let copydisplayValues = displayValues.map((item) => {
        return item * 1.05;
      });
      // 图表配置项
      const option = {
        // 提示框配置
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const item = params[0];
            const value = item.value;
            const count = displayCounts[item.dataIndex];
            return (
              '<div style="text-align:center;">' +
              `<span style="color:#ffce7c;font-size:16px;">${value}亿元</span><br/>` +
              `<span style="color:#7cebff;font-size:16px;">${count}笔</span>` +
              "</div>"
            );
          },
          backgroundColor: "rgba(10,29,56,0.95)",
          borderColor: "#0a4d8f",
          borderWidth: 1,
          extraCssText: "box-shadow:0 0 8px #0a4d8f;",
        },
        // 图表网格配置
        grid: {
          left: "0%",
          right: "0%",
          top: "30%",
          bottom: "2%",
          containLabel: true,
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: displayBanks,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            interval: 0,
            formatter: function (value) {
              return value.replace(/(.{4})/g, "$1\n");
            },
          },
        },
        // Y轴配置
        yAxis: {
          show: false,
        },
        // 系列配置
        series: [
          // 主柱状图 - 正面
          {
            name: "main",
            tooltip: {
              show: false,
            },
            type: "bar",
            barWidth: this.$autoFontSize(20),
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(42,205,253, 0.6)", // 底部颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(42,205,253, 0.8)", // 中间颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(42,205,253, 1)", // 顶部颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: displayValues,
          },
          // 右侧面 - 深度效果
          {
            name: "right",
            tooltip: {
              show: false,
            },
            type: "bar",
            barWidth: this.$autoFontSize(10),
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(42,205,253, 0.4)", // 底部颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(42,205,253, 0.6)", // 中间颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(42,205,253, 0.8)", // 顶部颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: copydisplayValues,
          },
          // 顶面 - 3D顶部效果
          {
            name: "top3d",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 2,
              borderColor: "#000",
              color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                {
                  offset: 0,
                  color: "rgba(42,205,253, 1)",
                },
                {
                  offset: 1,
                  color: "#419EFF",
                },
              ]),
            },
            symbol: "path://M 0,0 l 80,0 l -15,10 l -80,0 z",
            symbolSize: ["31", "7"],
            symbolOffset: ["-4", "-7"],
            /*      symbolRotate: -8, */
            symbolPosition: "end",
            data: displayValues,
            z: 3,
          },
          // 标签系列
          {
            type: "bar",
            label: {
              show: true,
              position: "top",
              padding: [0, 0, this.$autoFontSize(5), 0],
              formatter: (params) => {
                const value = displayValues[params.dataIndex];
                const count = displayCounts[params.dataIndex];
                return `{gold|${value}亿元}\n{blue|${count}}`;
              },
              rich: {
                gold: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [
                    0,
                    this.$autoFontSize(28),
                    this.$autoFontSize(5),
                    0,
                  ],
                  align: "center",
                },
                blue: {
                  color: "#7cebff",
                  padding: [0, this.$autoFontSize(28), 0, 0],
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
            itemStyle: {
              color: "transparent",
            },
            tooltip: {},
            data: copydisplayValues,
          },
        ],
      };
      this.chart.setOption(option);
      // 监听窗口大小变化，调整图表大小
      window.addEventListener("resize", () => this.chart.resize());
    },
    handleClick() {
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
    },
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.chart) {
      this.chart.dispose();
    }
    if (this.dialogAutoPlayTimer) {
      clearTimeout(this.dialogAutoPlayTimer);
    }
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.AmountTop5-dataAcquisition {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .AmountTop5-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .ChainDistribution-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      .ChainDistribution-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      .ChainDistribution-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .AmountTop5-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 1px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    display: flex;
    flex-direction: column;
    align-items: center;

    /* 顶部统计信息样式 */
    .AmountTop5-top-summary {
      width: 940px;
      text-align: left;
      font-size: 26px;
      color: #fff;
      margin-left: 100px;

      /* 金额高亮样式 */
      .AmountTop5-highlight {
        background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 笔数高亮样式 */
      .AmountTop5-highlight2 {
        margin-left: 20px;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    /* 图表容器样式 */
    .AmountTop5-echart-bar {
      width: 100%;
      height: 19vh;
      margin-top: 0;
    }
  }
}
</style>
